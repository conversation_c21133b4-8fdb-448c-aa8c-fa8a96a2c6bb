#NOP {梦境寻宝任务模块};
#ALIAS {jobgo_qhm} {
    #VARIABLE {currentjob} {情怀梦};
    #IF {@carryqty{da huandan} <= 1} {
      tbbuy {da huandan} {2} {gotodo {扬州城}{车马行} {startfull {jobqhm_f1}}}
    };
    #ELSE {
      gotodo {扬州城}{车马行} {startfull {jobqhm_f1}}
    }
};

#NOP {情怀梦境热身室};
#ALIAS {jobqhm_f1} {
    #CLASS jobaskclass KILL;
    #CLASS jobaskclass OPEN;
    #VARIABLE {idle} {0};
    #VARIABLE {jobstart_ts} {@now{}};
    #VARIABLE {jobnpc_count} {0};
    #VARIABLE {qhmfloor} {1};
    #ACTION {^请明天再来情怀梦境吧。} {
        #CLASS jobaskclass KILL;
        #VARIABLE {env[mendtime]} {@eval{@now{} - 86400 + 3600}};
        set env_fubentime @getFubenSets{};
        dohalt {jobcheck}
    };
    #CLASS jobaskclass CLOSE;
    #NOP {标记打npc的状态:1横跳,2偶遇,3出口};
    #VARIABLE {jobphase} {1};
    #NOP {标记移动失败的状态:1标识不能走路};
    #VARIABLE {movefail} {0};
    #NOP {宝藏房间已到达};
    #VARIABLE {treasuretouched} {0};
    #VARIABLE {pathbackexec} {1};
    qinghuaimeng;
    createpfm {$conf[pfm][scene][qhm]} {1};
    joblog {开始情怀梦境寻宝任务} {情怀梦};
    up;
    look;
    #DELAY {2} {
        #IF {"$room" == "情怀梦境"} {
            jobdo_qhm
        }
    }
};

#ALIAS {jobqhm_f2} {
    #VARIABLE {idle} {0};
    #VARIABLE {jobnpc_count} {0};
    #VARIABLE {qhmfloor} {2};
    #NOP {标记打npc的状态:1横跳,2偶遇,3出口};
    #VARIABLE {jobphase} {1};
    #NOP {标记移动失败的状态:1标识不能走路};
    #VARIABLE {movefail} {0};
    #NOP {宝藏房间已到达};
    #VARIABLE {treasuretouched} {0};
    #VARIABLE {pathbackexec} {1};
    createpfm {$conf[pfm][scene][qhm]} {1};
    joblog {开始情怀梦境二层寻宝任务};
    up2;
    look;
    #DELAY {2} {
        #IF {"$room" == "情怀梦境二层"} {
            jobdo_qhm
        }
    }
};

#NOP {createmazepath;gettreasure};
#NOP {从入口到宝藏};
#ALIAS {gotreasure} {
    #LIST {fullpath} {clear};
    #LIST {fullpath} {create} {$p1route};
    jobdo_qhm
};
#NOP {从宝藏到出口};
#ALIAS {goexit} {
    #LIST {fullpath} {clear};
    #LIST {fullpath} {create} {$p2route};
    jobdo_qhm
};
#NOP {打架};
#ALIAS {jobdo_qhm} {
    #CLASS jobaskclass KILL;
    #CLASS jobdoclass KILL;
    #CLASS jobdoclass OPEN;
    #ACTION {^  {梦境掌控者|梦游者} %*(%*)} {
        #ACTION {^{梦境掌控者|梦游者} %*(%*)} {
          #CLASS detaiscapclass OPEN;
          #ACTION {^这位掌控者来自%*。} {
            #VARIABLE {questnpc_wushi[%%%2][party]} {%%%%1}
          };
          #ACTION {^{他|她}穿戴着} {
            #VARIABLE {questnpc_wushi[%%%2][weapon]} {赤手空拳}
          };
          #ACTION {^{他|她}装备着} {
            #CLASS weaponcapclass OPEN;
            #ACTION {^  □%*(%*)} {
              #CLASS weaponcapclass KILL;
              #CLASS detaiscapclass KILL;
              #VARIABLE {questnpc_wushi[%%%2][weapon]} {%%%%%1}
            };
            #CLASS weaponcapclass CLOSE;
          };
          #CLASS detaiscapclass OPEN;
        };
        #VARIABLE {questnpc_wushi[%%2][name]} {%%2};
        #VARIABLE {questnpc_wushi[%%2][id]} {@lower{%%3}};
        #VARIABLE {questnpc_wushi[%%2][title]} {@lower{%%1}};
        #VARIABLE {movefail} {1}; 
        #VARIABLE {npcdetailscapped} {1};
        #IF {"$questnpc_wushi[%%2][party]" == ""} {
            look @lower{%%3}
        };
        #IF {"$questnpc_wushi[%%2][title]" == "梦游者"} {
            kill @lower{%%3}
        }
    };
    #ACTION {^看起来%*想杀死你！} {
        #VARIABLE {movefail} {1};
        #VARIABLE {notfighting} {0};
        startfight;
        #IF {@eval{$npcdetailscapped} == 0} {
            halt;
            look
        }
    };
    #ACTION {^%*一个闪身就不见了。} {
        #IF {"$questnpc_wushi[%%1][title]" == "梦境掌控者"} {
            joblog {战胜!} {情怀梦};
        };
        #UNTICKER {fight};
        #UNVARIABLE {questnpc_wushi[%%1]};
        #VARIABLE {jobnpc_count} {@eval{$jobnpc_count + 1}};
        #VARIABLE {movefail} {0};
        #VARIABLE {notfighting} {1};
        #VARIABLE {npcdetailscapped} {0};
        #VARIABLE {SupperNPC} {0};
        stopfight;
        #DELAY {3} {echo {checkfinish}}
    };
    #ACTION {^你仔细地勘探着宝藏，蹑手蹑脚地，很是担心破坏了里面的宝物！} {
        dohalt {search}
    };
    #ACTION {你满头大汉，终于到了宝藏之地，你可以在此(search)宝物!} {
        #VARIABLE {treasuretouched} {1};
    };
    #ACTION {^一阵时空的扭曲将你传送到另一个地方....} {
        #CLASS jobdoclass KILL;
        #VARIABLE {env[mendtime]}{@now{}};
        set env_fubentime @getFubenSets{};
        joblog {情怀梦境寻宝任务意外结束,耗时<ebf>@fomathms{@elapsed{$jobstart_ts}}} {情怀梦};
        #DELAY {10} {
            down;
            #IF {$testflag == 1} {
                loc {jobgo_qhm}
            };
            #ELSE {
                loc {jobcheck}
            }
        }
    };
    #ACTION {^你从宝藏中得到了一个%*。} {
        joblog {情怀梦境任务宝藏室获得%%1} {情怀梦};
        dohalt {search}
    };
    #ACTION {^你得到了%d枚情怀币,但是好像也触碰到了什么机关！} {
        #VARIABLE {jobreward_tongbao} {%%1};
        #VARIABLE {env[tongbap]} {@eval{$env[tongbap] + $jobreward_tongbao}};
        set env_tongbao Jobadd:$env[tongbap]|All:$hp[tongbao]|Pray:$env[pray];
        joblog {情怀梦境任务宝藏室获得<060>$jobreward_tongbao<900>通宝} {情怀梦};
        #VARIABLE {jobreward_tongbao}{0}
    };
    #ACTION {^{你伸手碰了碰宝藏入口，结果宝藏就坍塌了，你赶紧后退一步，结果还是被喷了满身白灰！|宝藏之地都被你破坏了，还找什么呢!}} {
        #VARIABLE {jobphase} {3};
        joblog {挖宝结束,寻路离开...} {情怀梦};
        #IF {"$p2route" != ""} {
            goexit
        };
        #ELSE {
            createmazepath {goexit}
        }
    };
    #ACTION {^{设定环境变量：action \= \"checkfinish\"|你设定checkfinish为反馈信息}} {
        yun qi;
        yun jing;
        yun jingli;
        hp;
        #VARIABLE {idle} {-120};
        #IF {@eval{$movefail} == 0} {
            #SWITCH {$jobphase} {
                #NOP {第1阶段:就地攒杀NPC};
                #NOP #SHOW <060>$jobnpc_count < 10;
                #CASE {1} {
                    #IF {$jobnpc_count < 10} {
                        #DELAY {1} {checkhp {joblinger_qhm}}
                    };
                    #ELSE {
                        joblog {共计定点杀死<030>$jobnpc_count<900>个游荡者,找宝藏};
                        #VARIABLE {jobphase} {2};
                        createmazepath {gotreasure}
                    }
                };
                #NOP {第2阶段:上路去宝藏室干掉偶遇NPC};
                #CASE {2} {
                    #NOP {路径OK继续找宝藏};
                    #IF {&fullpath[] > 0} {
                        $fullpath[+1];
                        #LIST {fullpath} {delete} {1};
                        #DELAY {1} {
                            echo {checkfinish}
                        }
                    };
                    #ELSEIF {&fullpath[] == 0 && $treasuretouched == 0} {
                        joblog {未到宝藏房间,重新寻找路径};
                        createmazepath {gotreasure}
                    };
                    #ELSE {
                        joblog {已经到宝藏房间,寻宝....};
                        search
                    }
                };
                #NOP {第3阶段:从宝藏室到出口干掉偶遇NPC};
                #CASE {3} {
                    #IF {&fullpath[] > 0} {
                        $fullpath[+1];
                        #LIST {fullpath} {delete} {1};
                        #DELAY {1} {
                            echo {checkfinish}
                        }
                    };
                    #ELSEIF {&fullpath[] == 0} {
                        i;
                        look;
                        #DELAY {2} {
                            #IF {@eval{&questnpc_wushi[]} == 0} {
                                #IF {&roomexits[] > 1} {
                                    joblog {未到出口房间,重新计算路径};
                                    createmazepath {goexit}
                                };
                                #ELSE {
                                    #IF {"$room" == "情怀梦境"} {
                                        joblog {情怀梦境第一层完结!};
                                        jobqhm_f2
                                    };
                                    #ELSE {
                                        joblog {情怀梦境第二层完结!};
                                        leave
                                    }
                                }
                            };
                            #ELSE {
                                printvar questnpc_wushi;
                                createpfm {$conf[pfm][scene][supernpc]} {1};
                                #VARIABLE {SupperNPC} {1};
                                joblog {准备解决梦境掌控者@partycolor{$questnpc_wushi[+1][party]}@namecolor{$questnpc_wushi[+1][name]}} {情怀梦};
                                checkhp {                                    
                                    startfight;
                                    kill $questnpc_wushi[+1][id];                                   
                                }
                            }
                        }
                    }
                }
            }
        }
    };
    #ACTION {^{你还是打发了游荡者再走吧！|你转身就要开溜，被%*一把拦住！}} {
        #VARIABLE {movefail} {1}
    };
    #ACTION {^你被奖励了%*枚情怀币！} {
        #VARIABLE {jobreward_tongbao} {@eval{$jobreward_tongbao + %%1}};
        #VARIABLE {env[tongbap]} {@eval{$env[tongbap] + $jobreward_tongbao}};
        set env_tongbao Jobadd:$env[tongbap]|All:$hp[tongbao]|Pray:$env[pray];
        joblog {情怀梦境寻宝任务获得<060>$jobreward_tongbao<900>通宝} {情怀梦};
        #VARIABLE {jobreward_tongbao}{0}
    };
    #ACTION {^你结束情怀梦境的冒险，回归到现实世界中!} {
        #DELAY {1} {
            #CLASS jobdoclass KILL;
            joblog {共计杀死<030>$jobnpc_count<900>个游荡者};
            joblog {完成情怀梦境寻宝任务,耗时<ebf>@fomathms{@elapsed{$jobstart_ts}}} {情怀梦};
            #VARIABLE {env[mendtime]}{@now{}};
            set env_fubentime @getFubenSets{};
            #IF {$testflag == 1} {
                loc {jobgo_qhm}
            };
            #ELSE {
                jobcheck
            }
        }
    };
    #CLASS jobdoclass CLOSE;
    #VARIABLE {movefail} {0};
    #VARIABLE {notfighting} {1};
    #VARIABLE {aimdo}{jobdo_qhm};
    #IF {$qhmfloor == 1} {
        #VARIABLE {aimroom}{情怀梦境}
    };
    #ELSE {
        #VARIABLE {aimroom}{情怀梦境二层}
    };
    echo {checkfinish}
};

#NOP {横跳等NPC};
#ALIAS {joblinger_qhm} {
    #UNVARIABLE {questnpc_wushi};
    #IF {@eval{$movefail} == 0} {
        #IF {@eval{$pathbackexec} == 1} {
            #VARIABLE {pathbackexec} {0};
            #NOP {去掉leave};
            #IF {@contains{{roomexits}{leave}} >  0} {
                #LIST {roomexits} {find} {leave} {tmp};
                #LIST {roomexits} {delete} {$tmp}
            };
            #VARIABLE {pathto} {$roomexits[+@rnd{{1}{&roomexits[]}}]};
            #VARIABLE {pathback} {@reverseDir{$pathto}};
            $pathto;
            #DELAY {1} {
                echo {checkfinish} 
             }
        };
        #ELSE {
            $pathback;
            #VARIABLE {pathbackexec} {1};
            #DELAY {1} {
                echo {checkfinish} 
             }
        }
    }
};

#NOP {计算迷宫路径};
#ALIAS {createmazepath} {
    #CLASS jobcapclass KILL;
    #CLASS jobcapclass OPEN;
    #NOP {创建地图房间};
    #LOOP {5000} {5240} {vnum} {#MAP DIG {$vnum}};
    #MAP GOTO {5000};
    #IF {"$room" == "情怀梦境"} {
        #ACTION {^C:当前位置 B:宝藏位置 E:出口位置} {
            #CLASS mazecap KILL;
            #CLASS mazecap OPEN;
            #VARIABLE {mazecount} {1};
            #VARIABLE {croom} {};
            #VARIABLE {broom} {};
            #UNVARIABLE {rawrow};
            #NOP {抓取字符迷宫数据};
            #ACTION {^ %*$} {
                #IF {"%%%1" != ""} {
                    #VARIABLE {rawrow[$mazecount]} {%%%1};
                    #NOP #REPLACE {rawrow[$mazecount]} { } {=};
                    #IF {$mazecount >= 23} {
                        #CLASS mazecap KILL;
                        #NOP {对抓取的字符迷宫数据格式化};
                        #IF {&rawrow[] > 0} {
                            #LIST {roomrow}{clear};
                            #FOREACH {*rawrow[]} {i} {
                                #LOCAL {x} {1};
                                #PARSE {$rawrow[$i]} {j} {
                                    #IF {$x <= 66} {
                                        #LIST {roomrow[$i]} {add} {$j};
                                        #MATH {x} {$x+1}
                                    }
                                }
                            }
                        };
                        #NOP printvar rawrow;
                        #NOP {初始化vnumrow数组，按照sjmap.tin中的房间号码模式分配};
                        #UNVARIABLE {vnumrow};
                        #FOREACH {*roomrow[]} {i} {
                            #LOCAL {x} {1};
                            #FOREACH {$roomrow[$i][]} {j} {
                                #NOP {房间号码 = (行号 * 100) + 列号 + 5000};
                                #VARIABLE {vnumrow[$i][$x]} {@eval{($i * 100) + $x + 5000}};
                                #MATH {x} {$x+1}
                            }
                        };
                        #NOP {遍历迷宫数据,根据具体的符号(房价和连接)进行连接地图房间};
                        #FOREACH {*roomrow[]} {i} {
                            #LOCAL {x} {1};
                            #FOREACH {$roomrow[$i][]} {j} {
                                #IF {"$j" == "C"} {
                                    #VARIABLE {croom} {$vnumrow[$i][$x]};
                                #NOP     #SHOW 当前位置C房间号:$croom,坐标:[$i][$x]
                                };
                                #ELSEIF {"$j" == "B"} {
                                    #VARIABLE {broom} {$vnumrow[$i][$x]};
                                #NOP     #SHOW 宝藏位置B房间号:$broom,坐标:[$i][$x]
                                };
                                #ELSEIF {"$j" == "E"} {
                                    #VARIABLE {eroom} {$vnumrow[$i][$x]};
                                #NOP     #SHOW 出口位置E房间号:$eroom,坐标:[$i][$x]
                                };
                                #ELSEIF {"$j" == "-"} {
                                #NOP #SHOW $vnumrow[$i][@eval{$x - 2}] <---> $vnumrow[$i][@eval{$x + 2}];
                                    #MAP AT {$vnumrow[$i][@eval{$x - 2}]} {#MAP LINK {e} {$vnumrow[$i][@eval{$x + 2}]} {both}}
                                };
                                #ELSEIF {"$j" == "|"} {
                                #NOP #SHOW $vnumrow[@eval{$i - 1}][$x]  <|> $vnumrow[@eval{$i + 1}][$x];
                                    #MAP AT {$vnumrow[@eval{$i - 1}][$x]} {#MAP LINK {s} {$vnumrow[@eval{$i + 1}][$x]} {both}}
                                };
                                #MATH {x} {$x+1}
                            }
                        };
                        #DELAY {0.1} {
                            #IF {@eval{$broom} > 0} {
                                #NOP {获取初始节点到宝藏的路径};
                                #VARIABLE {p1route} {@getWalkPath{{$croom}{$broom}}};
                                #SHOW <030>初始节点到宝藏的路径: $p1route;
                                #NOP {获取宝藏节点到出口的路径};
                                #VARIABLE {p2route} {@getWalkPath{{$broom}{$eroom}}};
                                #SHOW <030>宝藏节点到出口的路径: $p2route;
                            };
                            #ELSE {
                            #NOP {获取宝藏节点到出口的路径};
                            #VARIABLE {p2route} {@getWalkPath{{$croom}{$eroom}}};
                            #SHOW <030>宝藏节点到出口的路径: $p2route
                            };
                            #NOP {销毁迷宫地图房间};
                            #LOOP {5000} {5240} {vnum} {#MAP DELETE {$vnum}};
                            %1
                        }
                    };
                    #MATH {mazecount} {$mazecount + 1};
                }
            }{1};
            #CLASS mazecap CLOSE;
            #CLASS jobcapclass KILL;
        };
    };
    #ELSE {
        #LIST {tmproom} {create} {3;7;11;15;19;23;27;31;35;39;43;47;51;55;59;63};
        #LIST {tmproad} {create} {5;9;13;17;21;25;29;33;37;41;45;49;53;57;61;65};
        #ACTION {^----------------------------------------------------------------} {
            #CLASS mazecap KILL;
            #CLASS mazecap OPEN;
            #VARIABLE {mazecount} {1};
            #VARIABLE {croom} {};
            #VARIABLE {broom} {};
            #UNVARIABLE {rawrow};
            #NOP {抓取字符迷宫数据};
            #ACTION {^%*$} {
                #IF {"%%%1" != ""} {
                    #VARIABLE {rawrow[$mazecount]} {%%%1};
                    #NOP #REPLACE {rawrow[$mazecount]} { } {=};
                    #IF {$mazecount >= 24} {
                        #CLASS mazecap KILL;
                        #NOP {对抓取的字符迷宫数据格式化};
                        #IF {&rawrow[] > 0} {
                            #LIST {roomrow}{clear};
                            #FOREACH {*rawrow[]} {i} {
                                #IF {@eval{$i % 2} == 1} {
                                    #LOCAL {x} {1};
                                    #PARSE {$rawrow[$i]} {j} {
                                        #IF {$x <= 66 && $x > 1} {
                                            #IF {"$j" == " " && @contains{{tmproom}{$x}}} {
                                                #LIST {roomrow[$i]} {add} {O};
                                            };
                                            #ELSEIF {"$j" == " " && @contains{{tmproad}{$x}}} {
                                                #LIST {roomrow[$i]} {add} {#};
                                            };
                                            #ELSE {
                                                #LIST {roomrow[$i]} {add} {$j};
                                            }
                                        };
                                        #MATH {x} {$x+1}
                                    }
                                };
                                #ELSEIF {@eval{$i % 2} == 0} {
                                    #LOCAL {x} {1};
                                    #PARSE {$rawrow[$i]} {j} {
                                        #IF {$x <= 66 && $x > 1} {
                                            #IF {"$j" == " " && @contains{{tmproom}{$x}}} {
                                                #LIST {roomrow[$i]} {add} {*};
                                            };
                                            #ELSE {
                                                #LIST {roomrow[$i]} {add} {$j};
                                            }
                                        };
                                        #MATH {x} {$x+1}
                                    }
                                }
                            };
                        };
                        #NOP printvar rawrow;
                        #NOP {初始化vnumrow数组，按照sjmap.tin中的房间号码模式分配};
                        #UNVARIABLE {vnumrow};
                        #FOREACH {*roomrow[]} {i} {
                            #LOCAL {x} {1};
                            #FOREACH {$roomrow[$i][]} {j} {
                                #NOP {房间号码 = (行号 * 100) + 列号 + 5000，注意这里使用x-1作为列号};
                                #VARIABLE {vnumrow[$i][@eval{$x-1}]} {@eval{($i * 100) + $x + 5000}};
                                #MATH {x} {$x+1}
                            }
                        };
                        #NOP {遍历迷宫数据,根据具体的符号(房价和连接)进行连接地图房间};
                        #FOREACH {*roomrow[]} {i} {
                            #LOCAL {x} {1};
                            #FOREACH {$roomrow[$i][]} {j} {
                                #IF {"$j" == "C"} {
                                    #VARIABLE {croom} {$vnumrow[$i][@eval{$x-1}]};
                                  #NOP   #SHOW 当前位置C房间号:$croom,坐标:[$i][@eval{$x-1}]
                                };
                                #ELSEIF {"$j" == "B"} {
                                    #VARIABLE {broom} {$vnumrow[$i][@eval{$x-1}]};
                                  #NOP   #SHOW 宝藏位置B房间号:$broom,坐标:[$i][@eval{$x-1}]
                                };
                                #ELSEIF {"$j" == "E"} {
                                    #VARIABLE {eroom} {$vnumrow[$i][@eval{$x-1}]};
                                  #NOP   #SHOW 出口位置E房间号:$eroom,坐标:[$i][@eval{$x-1}]
                                };
                                #ELSEIF {"$j" == "#"} {
                                    #NOP #SHOW $vnumrow[$i][@eval{$x - 3}] <---> $vnumrow[$i][@eval{$x + 1}];
                                    #MAP AT {$vnumrow[$i][@eval{$x - 3}]} {#MAP LINK {e} {$vnumrow[$i][@eval{$x + 1}]} {both}}
                                };
                                #ELSEIF {"$j" == "*"} {
                                    #NOP #SHOW $vnumrow[@eval{$i - 1}][@eval{$x - 1}] | $vnumrow[@eval{$i + 1}][@eval{$x - 1}];
                                    #MAP AT {$vnumrow[@eval{$i - 1}][@eval{$x - 1}]} {#MAP LINK {s} {$vnumrow[@eval{$i + 1}][@eval{$x - 1}]} {both}}
                                };
                                #MATH {x} {$x+1}
                            }
                        };
                        #DELAY {0.1} {
                            #IF {@eval{$broom} > 0} {
                                #NOP {获取初始节点到宝藏的路径};
                                #VARIABLE {p1route} {@getWalkPath{{$croom}{$broom}}};
                                #SHOW <030>初始节点到宝藏的路径: $p1route;
                                #NOP {获取宝藏节点到出口的路径};
                                #VARIABLE {p2route} {@getWalkPath{{$broom}{$eroom}}};
                                #SHOW <030>宝藏节点到出口的路径: $p2route;
                            };
                            #ELSE {
                            #NOP {获取宝藏节点到出口的路径};
                            #VARIABLE {p2route} {@getWalkPath{{$croom}{$eroom}}};
                            #SHOW <030>宝藏节点到出口的路径: $p2route
                            };
                            #NOP {销毁迷宫地图房间};
                            #LOOP {5000} {5240} {vnum} {#MAP DELETE {$vnum}};
                            %1
                        }
                    };
                    #MATH {mazecount} {$mazecount + 1};
                }
            }{1};
            #CLASS mazecap CLOSE;
            #CLASS jobcapclass KILL;
        };
    };
    #CLASS jobcapclass CLOSE;
    mazemap
};

#ALIAS {testqhm} {
    #VARIABLE {testflag} {1};
    loc {jobgo_qhm}
};
#SHOWME {<fac>@padRight{{梦境寻宝任务}{12}}<fac> <cfa>模块加载完毕<cfa>};

